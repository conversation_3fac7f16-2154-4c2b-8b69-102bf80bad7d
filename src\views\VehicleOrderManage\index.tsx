import React, { useState, useEffect, useRef, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '@/redux/store';
import { message } from 'antd';
import { CommonTable, CommonForm, useTableData } from '@jd/x-coreui';
import CustomTabs from '@/components/CustomTabs';
import {
  searchConfig,
  tableColumns,
  tabsConfig,
  defaultHiddenColumns,
  defaultLeftFixedColumns,
  defaultRightFixedColumns,
  statusNameStyle,
} from './utils/constant';
import { vehicleOrderManageApi, commonApi } from '@/fetch/business';
import { HttpStatusCode } from '@/fetch/core/constant';
import {
  sortColumnsByState,
  createDefaultColumnsState,
} from '@jd/x-coreui/lib/components/CommonTable/columnUtils';
import { saveSearchValues } from '@/redux/reducer/searchForm';
import { formatDateToSecond } from '@/utils/utils';
import './index.scss';

const initSearchCondition = {
  // 表单组件所需特殊数据结构的字段
  provinceCityCountry: null,
  provinceAgencyArea: null,
  createTime: null,
  expectedDeliveryMonth: null,
  // 列表接口入参字段
  searchForm: {
    stationNumber: null,
    vehicleModelType: null,
    orderNumber: '',
    startTime: '',
    endTime: '',
    provinceId: null,
    cityId: null,
    countryId: null,
    companyCode: '',
    areaCode: '',
    expectedDeliveryMonth: '',
    status: '',
  },
  // 页码字段
  pageNum: 1,
  pageSize: 10,
};

// 下拉框数据映射
const dropdownDataMap = {
  provinceCityCountry: {
    fetchApi: commonApi.getProvinceCityCountryList(),
    options: [],
  },
  provinceAgencyArea: {
    fetchApi: commonApi.getProvinceAgencyAreaList(),
    options: [],
  },
  stationNumber: {
    fetchApi: commonApi.getStationList(),
    options: [],
  },
  vehicleModelType: {
    fetchApi: commonApi.getVehicleModelList(),
    options: [],
  },
};

const VehicleOrderManage = () => {
  const dispatch = useDispatch();
  const historySearchValues = useSelector(
    (state: RootState) => state.searchForm,
  );
  const navigator = useNavigate();
  const searchFormRef = useRef<any>(null);
  const searchRef = useRef<any>(null);

  const [searchCondition, setSearchCondition] = useState(() => {
    console.log('有历史数据吗', historySearchValues);
    return historySearchValues.searchValues
      ? historySearchValues.searchValues
      : initSearchCondition;
  });
  const [activeTabKey, setActiveTabKey] = useState<string>(() => {
    return historySearchValues.activeTabKey
      ? historySearchValues.activeTabKey
      : '';
  });
  const [countMap, setCountMap] = useState({
    TOTAL: 0,
    CREATED: 0,
    WAIT_SHIPMENT: 0,
    SHIPPED: 0,
    ARRIVED: 0,
    COMPLETED: 0,
  });
  const [selectInfo, setSelectInfo] = useState<{
    selectedRowKeys: any[];
    selectedRows: any[];
    clearFunc: any;
  }>({
    selectedRowKeys: [],
    selectedRows: [],
    clearFunc: () => {},
  });
  const [searchFormConfig, setSearchFormConfig] = useState(searchConfig);

  const defaultColumnsState = useMemo(() => {
    return createDefaultColumnsState(
      tableColumns,
      defaultHiddenColumns,
      defaultLeftFixedColumns,
      defaultRightFixedColumns,
    );
  }, []);
  const [columnsState, setColumnsState] = useState<any>(defaultColumnsState);

  // 使用真实API进行开发
  const { tableData, loading } = useTableData(
    {
      ...searchCondition.searchForm,
      pageNum: searchCondition.pageNum,
      pageSize: searchCondition.pageSize,
    },
    vehicleOrderManageApi.getVehicleOrderPage,
  );

  useEffect(() => {
    initializeDropdownData();
  }, []);

  useEffect(() => {
    if ((tableData as any)?.countMap) {
      setCountMap((tableData as any).countMap);
    }
  }, [tableData]);

  const initializeDropdownData = async () => {
    try {
      const allApi = Object.values(dropdownDataMap).map(
        (item) => item.fetchApi,
      );
      const allRes = await Promise.all(allApi);
      const dropdownKeys = Object.keys(dropdownDataMap);
      allRes?.forEach((res, index) => {
        if (res && res.code === HttpStatusCode.Success && res.data) {
          const currentKey = dropdownKeys[index];
          dropdownDataMap[currentKey].options = res.data;
        }
      });
      const newSearchConfig = {
        ...searchConfig,
        fields: searchConfig.fields.map((field) => {
          switch (field.fieldName) {
            case 'provinceCityCountry':
              return {
                ...field,
                options: dropdownDataMap.provinceCityCountry.options,
              };
            case 'provinceAgencyArea':
              return {
                ...field,
                options: dropdownDataMap.provinceAgencyArea.options,
              };
            case 'stationNumber':
              return {
                ...field,
                options: dropdownDataMap.stationNumber.options,
              };
            case 'vehicleModelType':
              return {
                ...field,
                options: dropdownDataMap.vehicleModelType.options,
              };
            default:
              return field;
          }
        }),
      };
      setSearchFormConfig(newSearchConfig);
    } catch (error) {
      console.error('初始化下拉框数据失败:', error);
      message.error('初始化下拉框数据失败');
    }
  };

  const onSearchClick = (values: any) => {
    const newCondition = formatSearchValues(values, { pageNum: 1 });
    setSearchCondition(newCondition);
    saveHistorySearchValue(newCondition);
  };

  const onResetClick = () => {
    const resetValues = {
      ...initSearchCondition,
      searchForm: {
        ...initSearchCondition.searchForm,
        status: activeTabKey || '',
      },
    };
    setSearchCondition(resetValues);
    saveHistorySearchValue(resetValues);
    if (searchFormRef.current) {
      searchFormRef.current.resetFields();
    }
  };

  const formatSearchValues = (values: any, currentData: any) => {
    const {
      createTime,
      provinceCityCountry,
      provinceAgencyArea,
      expectedDeliveryMonth,
      ...otherValues
    } = values;
    const { status, pageNum, pageSize } = currentData;
    const formatTime: any =
      createTime?.length > 0 ? formatDateToSecond(createTime) : {};
    const newSearchCondition = {
      createTime,
      provinceCityCountry,
      provinceAgencyArea,
      expectedDeliveryMonth,
      searchForm: {
        ...otherValues,
        startTime: formatTime.startTime
          ? `${formatTime.startTime.split(' ')[0]} 00:00:00`
          : '',
        endTime: formatTime.endTime
          ? `${formatTime.endTime.split(' ')[0]} 23:59:59`
          : '',
        provinceId: provinceCityCountry?.[0] || null,
        cityId: provinceCityCountry?.[1] || null,
        countryId: provinceCityCountry?.[2] || null,
        companyCode: provinceAgencyArea?.[0] || '',
        areaCode: provinceAgencyArea?.[1] || '',
        expectedDeliveryMonth: expectedDeliveryMonth
          ? expectedDeliveryMonth.format('YYYY-MM')
          : '',
        status: status === undefined || status === null ? activeTabKey : status,
      },
      pageNum:
        pageNum === undefined || pageNum === null
          ? searchCondition.pageNum
          : pageNum,
      pageSize:
        pageSize === undefined || pageSize === null
          ? searchCondition.pageSize
          : pageSize,
    };
    return newSearchCondition;
  };

  const handleTabChange = (key: string) => {
    setActiveTabKey(key);
    const currentSearchForm = searchFormRef.current?.getFieldsValue() || {};
    const newCondition = formatSearchValues(currentSearchForm, {
      pageNum: 1,
      status: key,
    });
    setSearchCondition(newCondition);
    saveHistorySearchValue(newCondition, key);
    selectInfo.clearFunc();
    setSelectInfo({
      selectedRowKeys: [],
      selectedRows: [],
      clearFunc: () => {},
    });
  };

  const renderOperationButtons = (record: any) => {
    const baseBtnConfig = [
      {
        key: 'detail',
        text: '查看详情',
        onClick: () => {
          console.log('查看详情', record);
          // 跳转到订单详情页面，传递订单id、订单状态和type参数
          const params = new URLSearchParams({
            id: record.id.toString(),
            status: record.status,
          });
          navigator(
            `/app/vehicleOrderManage/vehicleOrderDetail?${params.toString()}`,
          );
        },
      },
    ];
    let btnConfig = [...baseBtnConfig];
    switch (record.status) {
      case 'CREATED':
        btnConfig.unshift({
          key: 'allocate',
          text: '配车',
          onClick: () => {
            console.log('配车操作', record);
            // 跳转到配车页面，传递必要参数
            const params = new URLSearchParams({
              id: record.id.toString(),
              count: record.count.toString(),
              vehicleModel: record.vehicleModelName || '',
            });
            navigator(
              `/app/vehicleOrderManage/configureVehicle?${params.toString()}`,
            );
          },
        });
        break;
      case 'WAIT_SHIPMENT':
        btnConfig.unshift({
          key: 'dispatch',
          text: '发运',
          onClick: () => {
            console.log('发运操作', record);
            // 跳转到发运页面，传递必要参数
            const params = new URLSearchParams({
              id: record.id.toString(),
              count: record.count.toString(),
              vehicleModel: record.vehicleModelName || '',
              allocationCount: record.allocationCount?.toString() || '0',
            });
            navigator(
              `/app/vehicleOrderManage/shippingVehicle?${params.toString()}`,
            );
          },
        });
        break;
      case 'SHIPPED':
        btnConfig.unshift({
          key: 'arrive',
          text: '到车',
          onClick: () => {
            console.log('到车操作', record);
            message.info('到车功能待开发');
          },
        });
        break;
      case 'ARRIVED':
        btnConfig.unshift({
          key: 'accept',
          text: '验收',
          onClick: () => {
            console.log('验收操作', record);
            // 跳转到验收页面，传递订单id参数
            const params = new URLSearchParams({
              id: record.id.toString(),
            });
            navigator(
              `/app/vehicleOrderManage/vehicleAcceptance?${params.toString()}`,
            );
          },
        });
        break;
      default:
        break;
    }
    return (
      <>
        {btnConfig.map((btn, index) => (
          <a key={btn.key} onClick={btn.onClick}>
            {btn.text}
          </a>
        ))}
      </>
    );
  };

  const formatColumns = useMemo(() => {
    return tableColumns.map((col) => {
      switch (col?.dataIndex) {
        case 'operation':
          return {
            ...col,
            render: (_: any, record: any) => (
              <div className="operate">{renderOperationButtons(record)}</div>
            ),
          };
        case 'statusName':
          return {
            ...col,
            render: (text: any, record: any) => {
              const textColor =
                statusNameStyle[record?.status]?.textColor ||
                'rgba(0, 0, 0, 0.88)';
              const bgColor =
                statusNameStyle[record?.status]?.bgColor || '#fff';
              return text ? (
                <div
                  style={{
                    color: textColor,
                    backgroundColor: bgColor,
                    borderRadius: '5px',
                    width: 'fit-content',
                    padding: '3px',
                  }}
                >
                  {text}
                </div>
              ) : (
                '-'
              );
            },
          };
        default:
          return {
            ...col,
            render: (text: any) => `${text ?? '-'}`,
          };
      }
    });
  }, []);

  const dynamicColumns = useMemo(() => {
    return sortColumnsByState(formatColumns, columnsState) || [];
  }, [formatColumns, columnsState]);

  const saveHistorySearchValue = (
    searchValues: any,
    currentTabKey?: string,
  ) => {
    dispatch(
      saveSearchValues({
        routeName: location.pathname,
        searchValues,
        activeTabKey:
          currentTabKey === undefined || currentTabKey === null
            ? activeTabKey
            : currentTabKey,
      }),
    );
  };

  const formatTabsConfig = useMemo(() => {
    return tabsConfig.map((tab) => ({
      key: tab.key,
      label: tab.label,
      count: countMap?.[tab.statusKey as keyof typeof countMap] || 0,
    }));
  }, [countMap]);

  return (
    <div className="vehicle-order-manage">
      <div ref={searchRef}>
        <CommonForm
          formConfig={searchFormConfig}
          defaultValue={{
            ...searchCondition,
            ...searchCondition.searchForm,
          }}
          layout="inline"
          formType="search"
          getFormInstance={(ref) => (searchFormRef.current = ref)}
          onSearchClick={onSearchClick}
          onResetClick={onResetClick}
        />
      </div>
      <div className="table-container">
        <CustomTabs
          items={formatTabsConfig}
          activeKey={activeTabKey}
          onChange={handleTabChange}
        />
        <CommonTable
          tableListData={{
            list: (tableData as any)?.list ?? [],
            totalNumber: (tableData as any)?.total,
            totalPage: (tableData as any)?.pages,
          }}
          tableKey={'vehicle-order-manage-table'}
          columns={dynamicColumns}
          loading={loading}
          rowKey="id"
          searchCondition={searchCondition}
          onPageChange={(value: any) => {
            console.log('此时的分页变化', value);
            saveHistorySearchValue(value);
            setSearchCondition(value);
          }}
          crossPageSelect={(keys: any, rows: any, clearFunc: any) => {
            setSelectInfo({
              selectedRowKeys: keys,
              selectedRows: rows,
              clearFunc: clearFunc,
            });
          }}
          // 列配置相关属性
          showColumnSetting={true}
          columnsState={{
            value: columnsState,
            onChange: setColumnsState,
            persistenceType: 'localStorage',
          }}
          defaultColumnsState={defaultColumnsState}
          searchRef={searchRef}
        />
      </div>
    </div>
  );
};

export default React.memo(VehicleOrderManage);
